#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket数据流阻塞详细日志追踪系统
增强原有的websocket_logger.py，添加精确的阻塞原因检测和记录
"""

import time
import json
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

@dataclass
class BlockingEvent:
    """数据流阻塞事件"""
    exchange: str
    market_type: str
    symbol: str
    start_time: float
    end_time: Optional[float]
    duration: float
    blocking_type: str  # 'connection', 'subscription', 'data_flow', 'rate_limit'
    cause: str
    context: Dict[str, Any]
    severity: str  # 'low', 'medium', 'high', 'critical'

@dataclass
class DataFlowMetrics:
    """数据流指标"""
    exchange: str
    last_data_time: float
    total_messages: int
    messages_per_second: float
    connection_drops: int
    subscription_failures: int
    error_count: int
    avg_latency: float

class WebSocketBlockingTracker:
    """WebSocket数据流阻塞追踪器"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # 🔥 设置专用的阻塞检测日志
        self.blocking_logger = self._setup_blocking_logger()
        self.metrics_logger = self._setup_metrics_logger()
        
        # 🔥 阻塞检测配置
        self.blocking_threshold = 30  # 30秒无数据认为阻塞
        self.critical_threshold = 120  # 120秒认为严重阻塞
        
        # 🔥 跟踪各交易所状态
        self.exchange_metrics: Dict[str, DataFlowMetrics] = {}
        self.active_blocking_events: Dict[str, BlockingEvent] = {}
        self.blocking_history: List[BlockingEvent] = []
        
        # 🔥 性能监控
        self.monitoring_start_time = time.time()
        self.message_counts = {}
        self.last_metrics_log = time.time()
        
        self.blocking_logger.info("🔍 WebSocket数据流阻塞追踪器已启动")
        
    def _setup_blocking_logger(self) -> logging.Logger:
        """设置阻塞检测专用日志器"""
        logger = logging.getLogger("websocket_blocking_tracker")
        logger.setLevel(logging.DEBUG)
        
        # 避免重复添加处理器
        if logger.handlers:
            return logger
            
        # 文件处理器 - 阻塞事件日志
        blocking_file = self.log_dir / f"websocket_blocking_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.FileHandler(blocking_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # 控制台处理器 - 只显示重要阻塞事件
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.WARNING)
        
        # 格式器
        detailed_formatter = logging.Formatter(
            '%(asctime)s - BLOCKING - %(levelname)s - %(message)s'
        )
        console_formatter = logging.Formatter(
            '🚨 %(asctime)s - %(message)s'
        )
        
        file_handler.setFormatter(detailed_formatter)
        console_handler.setFormatter(console_formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def _setup_metrics_logger(self) -> logging.Logger:
        """设置性能指标日志器"""
        logger = logging.getLogger("websocket_metrics_tracker")
        logger.setLevel(logging.INFO)
        
        # 避免重复添加处理器
        if logger.handlers:
            return logger
            
        # 性能指标日志文件
        metrics_file = self.log_dir / f"websocket_metrics_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.FileHandler(metrics_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        formatter = logging.Formatter(
            '%(asctime)s - METRICS - %(message)s'
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        return logger
        
    def update_exchange_metrics(self, exchange: str, market_type: str = "unknown", 
                              symbol: str = "unknown", message_data: Dict = None):
        """更新交易所数据流指标"""
        current_time = time.time()
        key = f"{exchange}_{market_type}"
        
        if key not in self.exchange_metrics:
            self.exchange_metrics[key] = DataFlowMetrics(
                exchange=exchange,
                last_data_time=current_time,
                total_messages=0,
                messages_per_second=0.0,
                connection_drops=0,
                subscription_failures=0,
                error_count=0,
                avg_latency=0.0
            )
            
        metrics = self.exchange_metrics[key]
        
        # 🔥 检测数据流阻塞
        if metrics.last_data_time > 0:
            gap = current_time - metrics.last_data_time
            
            if gap > self.blocking_threshold:
                self._handle_blocking_detected(exchange, market_type, symbol, gap, current_time)
                
        # 更新指标
        metrics.last_data_time = current_time
        metrics.total_messages += 1
        
        # 计算消息频率
        runtime = current_time - self.monitoring_start_time
        if runtime > 0:
            metrics.messages_per_second = metrics.total_messages / runtime
            
        # 🔥 记录详细的数据接收日志
        self.blocking_logger.debug(
            f"📊 {exchange}_{market_type} 数据更新: symbol={symbol}, "
            f"总消息={metrics.total_messages}, 频率={metrics.messages_per_second:.2f}/s"
        )
        
    def _handle_blocking_detected(self, exchange: str, market_type: str, symbol: str, 
                                gap: float, current_time: float):
        """处理检测到的数据流阻塞"""
        key = f"{exchange}_{market_type}_{symbol}"
        
        # 🔥 判断阻塞严重程度
        if gap > self.critical_threshold:
            severity = "critical"
        elif gap > 60:
            severity = "high"
        elif gap > 30:
            severity = "medium"
        else:
            severity = "low"
            
        # 🔥 分析可能的阻塞原因
        cause = self._analyze_blocking_cause(exchange, market_type, gap)
        
        # 创建阻塞事件
        blocking_event = BlockingEvent(
            exchange=exchange,
            market_type=market_type,
            symbol=symbol,
            start_time=self.exchange_metrics[f"{exchange}_{market_type}"].last_data_time,
            end_time=current_time,
            duration=gap,
            blocking_type="data_flow",
            cause=cause,
            context={
                "gap_seconds": gap,
                "message_count": self.exchange_metrics[f"{exchange}_{market_type}"].total_messages,
                "detection_time": datetime.now().isoformat()
            },
            severity=severity
        )
        
        # 记录阻塞事件
        self._log_blocking_event(blocking_event)
        self.blocking_history.append(blocking_event)
        
        # 🔥 如果是严重阻塞，立即记录到控制台
        if severity in ["high", "critical"]:
            self.blocking_logger.warning(
                f"🚨 {severity.upper()}阻塞检测: {exchange}_{market_type} "
                f"已{gap:.1f}秒无数据, 原因: {cause}"
            )
            
    def _analyze_blocking_cause(self, exchange: str, market_type: str, gap: float) -> str:
        """分析数据流阻塞的可能原因"""
        key = f"{exchange}_{market_type}"
        metrics = self.exchange_metrics.get(key)
        
        if not metrics:
            return "未知原因"
            
        # 🔥 **通用化修复**：移除交易所特定处理，使用通用原因分析
        possible_causes = []

        # 🔥 **通用频率限制检查**：基于阻塞时长而非交易所特定规则
        if gap > 60:
            possible_causes.append("长时间阻塞可能由API频率限制引起")
        elif gap > 30:
            possible_causes.append("中等时长阻塞可能由订阅频率过高引起")
        elif gap > 15:
            possible_causes.append("短时间阻塞可能由网络延迟引起")
            
        # 检查连接相关问题
        if metrics.connection_drops > 0:
            possible_causes.append("连接频繁断开")
            
        if metrics.subscription_failures > 0:
            possible_causes.append("订阅失败累积")
            
        if metrics.error_count > 5:
            possible_causes.append("错误频发")
            
        # 检查消息频率异常
        if metrics.messages_per_second < 0.1:
            possible_causes.append("消息频率异常低")
            
        return "; ".join(possible_causes) if possible_causes else "原因不明，需进一步调查"
    
    def _log_blocking_event(self, event: BlockingEvent):
        """记录阻塞事件的详细日志"""
        event_json = json.dumps(asdict(event), indent=2, ensure_ascii=False)
        
        self.blocking_logger.info(
            f"🔍 阻塞事件记录: {event.exchange}_{event.market_type}\n"
            f"   持续时间: {event.duration:.1f}秒\n"
            f"   严重程度: {event.severity}\n" 
            f"   可能原因: {event.cause}\n"
            f"   详细信息: {event_json}"
        )
        
    def log_connection_event(self, exchange: str, market_type: str, event_type: str, 
                           details: Dict = None):
        """记录连接相关事件"""
        key = f"{exchange}_{market_type}"
        
        if key in self.exchange_metrics:
            if event_type == "disconnect":
                self.exchange_metrics[key].connection_drops += 1
            elif event_type == "subscription_failure":
                self.exchange_metrics[key].subscription_failures += 1
            elif event_type == "error":
                self.exchange_metrics[key].error_count += 1
                
        self.blocking_logger.info(
            f"🔗 连接事件: {exchange}_{market_type} - {event_type}"
            f"{f' - {details}' if details else ''}"
        )
        
        # 🔥 **关键修复**：对于数据过期事件，创建阻塞事件记录
        if event_type == "data_staleness_detected" and details:
            duration = details.get('silent_duration_seconds', 0)
            if duration > 120:  # 只有超过120秒的才算真正阻塞
                blocking_event = BlockingEvent(
                    exchange=exchange,
                    market_type=market_type,
                    symbol="all",  # 时间戳处理器层面的阻塞影响所有交易对
                    start_time=time.time() - duration,
                    end_time=time.time(),
                    duration=duration,
                    blocking_type="data_staleness",
                    cause=f"数据时间戳过期{duration:.1f}秒，来源：时间戳处理器",
                    context=details,
                    severity="high" if duration > 60 else "medium"
                )
                
                # 记录阻塞事件
                self._log_blocking_event(blocking_event)
                self.blocking_history.append(blocking_event)
                
                self.blocking_logger.warning(
                    f"🚨 数据过期阻塞: {exchange} 数据过期{duration:.1f}秒"
                )
        
    def log_subscription_attempt(self, exchange: str, market_type: str, symbols: List[str], 
                               success: bool, details: Dict = None):
        """记录订阅尝试"""
        status = "成功" if success else "失败"
        symbol_count = len(symbols)
        
        self.blocking_logger.info(
            f"🔔 订阅尝试: {exchange}_{market_type} - {symbol_count}个交易对 - {status}"
            f"{f' - {details}' if details else ''}"
        )
        
        if not success:
            self.log_connection_event(exchange, market_type, "subscription_failure", details)
            
    def log_rate_limit_violation(self, exchange: str, market_type: str, 
                               violation_type: str, details: Dict = None):
        """记录频率限制违规"""
        self.blocking_logger.warning(
            f"⚠️ 频率限制违规: {exchange}_{market_type} - {violation_type}"
            f"{f' - {details}' if details else ''}"
        )
        
        # 创建限速阻塞事件
        blocking_event = BlockingEvent(
            exchange=exchange,
            market_type=market_type,
            symbol="all",
            start_time=time.time(),
            end_time=None,
            duration=0,
            blocking_type="rate_limit",
            cause=f"频率限制违规: {violation_type}",
            context=details or {},
            severity="high"
        )
        
        self._log_blocking_event(blocking_event)
        self.blocking_history.append(blocking_event)
        
    def generate_periodic_report(self, force: bool = False):
        """生成周期性的数据流状态报告"""
        current_time = time.time()
        
        # 每5分钟生成一次报告
        if not force and current_time - self.last_metrics_log < 300:
            return
            
        self.last_metrics_log = current_time
        runtime = current_time - self.monitoring_start_time
        
        # 🔥 生成综合状态报告
        total_messages = sum(m.total_messages for m in self.exchange_metrics.values())
        total_blocking_events = len(self.blocking_history)
        
        self.metrics_logger.info(
            f"📊 数据流状态报告 (运行时间: {runtime:.0f}秒):\n"
            f"   总消息数: {total_messages}\n"
            f"   阻塞事件: {total_blocking_events}次\n"
            f"   活跃交易所: {len(self.exchange_metrics)}个"
        )
        
        # 各交易所详细状态
        for key, metrics in self.exchange_metrics.items():
            recent_gap = current_time - metrics.last_data_time
            status = "🟢正常" if recent_gap < 30 else "🔴可能阻塞"
            
            self.metrics_logger.info(
                f"   {key}: {status} | "
                f"消息={metrics.total_messages} | "
                f"频率={metrics.messages_per_second:.2f}/s | "
                f"最后数据={recent_gap:.0f}秒前"
            )
            
    def get_blocking_summary(self) -> Dict[str, Any]:
        """获取阻塞情况摘要"""
        current_time = time.time()
        
        summary = {
            "total_blocking_events": len(self.blocking_history),
            "critical_blocks": len([e for e in self.blocking_history if e.severity == "critical"]),
            "high_blocks": len([e for e in self.blocking_history if e.severity == "high"]),
            "exchanges_status": {},
            "most_common_causes": {},
            "report_time": datetime.now().isoformat()
        }
        
        # 统计各交易所状态
        for key, metrics in self.exchange_metrics.items():
            gap = current_time - metrics.last_data_time
            summary["exchanges_status"][key] = {
                "status": "normal" if gap < 30 else "blocked",
                "last_data_gap": gap,
                "total_messages": metrics.total_messages,
                "message_rate": metrics.messages_per_second
            }
            
        # 统计最常见的阻塞原因
        causes = [event.cause for event in self.blocking_history]
        cause_counts = {}
        for cause in causes:
            for individual_cause in cause.split("; "):
                cause_counts[individual_cause] = cause_counts.get(individual_cause, 0) + 1
                
        summary["most_common_causes"] = dict(sorted(cause_counts.items(), 
                                                  key=lambda x: x[1], reverse=True)[:5])
        
        return summary


# 🔥 全局单例实例
_blocking_tracker_instance = None

def get_blocking_tracker() -> WebSocketBlockingTracker:
    """获取数据流阻塞追踪器单例"""
    global _blocking_tracker_instance
    if _blocking_tracker_instance is None:
        _blocking_tracker_instance = WebSocketBlockingTracker()
    return _blocking_tracker_instance

def log_websocket_data_received(exchange: str, market_type: str = "unknown", 
                               symbol: str = "unknown", message_data: Dict = None):
    """记录WebSocket数据接收 - 供外部调用"""
    tracker = get_blocking_tracker()
    tracker.update_exchange_metrics(exchange, market_type, symbol, message_data)

def log_websocket_connection_event(exchange: str, market_type: str, event_type: str, 
                                 details: Dict = None):
    """记录WebSocket连接事件 - 供外部调用"""
    tracker = get_blocking_tracker()
    tracker.log_connection_event(exchange, market_type, event_type, details)

def log_websocket_subscription_attempt(exchange: str, market_type: str, symbols: List[str], 
                                     success: bool, details: Dict = None):
    """记录WebSocket订阅尝试 - 供外部调用"""
    tracker = get_blocking_tracker()
    tracker.log_subscription_attempt(exchange, market_type, symbols, success, details)

def log_websocket_rate_limit_violation(exchange: str, market_type: str, 
                                     violation_type: str, details: Dict = None):
    """记录频率限制违规 - 供外部调用"""
    tracker = get_blocking_tracker()
    tracker.log_rate_limit_violation(exchange, market_type, violation_type, details)

# 🔥 为了兼容现有代码，保留原有函数名
def log_websocket_subscription_failure(level: str, message: str, **kwargs):
    """兼容原有的订阅失败日志函数"""
    exchange = kwargs.get('exchange', 'unknown')
    market_type = kwargs.get('market_type', 'unknown')
    
    details = {
        "level": level,
        "message": message,
        "context": kwargs
    }
    
    log_websocket_connection_event(exchange, market_type, "subscription_failure", details)


if __name__ == "__main__":
    # 测试脚本
    print("🧪 WebSocket阻塞追踪器测试")
    
    tracker = get_blocking_tracker()
    
    # 模拟正常数据流
    for i in range(5):
        log_websocket_data_received("gate", "spot", "BTC_USDT", {"test": i})
        time.sleep(1)
        
    # 模拟阻塞
    print("⏳ 模拟数据流阻塞...")
    time.sleep(35)  # 超过30秒阈值
    
    # 恢复数据流
    log_websocket_data_received("gate", "spot", "BTC_USDT", {"test": "recovery"})
    
    # 生成报告
    tracker.generate_periodic_report(force=True)
    summary = tracker.get_blocking_summary()
    
    print("📊 阻塞摘要:")
    print(json.dumps(summary, indent=2, ensure_ascii=False))